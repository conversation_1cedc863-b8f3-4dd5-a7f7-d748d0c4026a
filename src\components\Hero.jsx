import { useState, useEffect } from 'react'
import { Star, Eye, CheckCircle, Users, MessageCircle, Play, Shield, Calendar, Clock, Headphones, Bot, Store } from 'lucide-react'
import { Button } from './ui/button'

const Hero = () => {
  const [countdown, setCountdown] = useState({ hours: 47, minutes: 23, seconds: 44 })
  const [currentMessage, setCurrentMessage] = useState(0)

  const messages = [
    'Perfeito! Temos soluções para diferentes tipos de negócio:\n• Odontologia\n• Sistema Financeiro\n• Veterinárias\n• E muito mais!\n\nQual área te interessa mais?',
    'Ótimo! Para odontologia, posso ajudar com:\n• Agendamento de consultas\n• Lembretes automáticos\n• Orientações pós-procedimento\n• Dicas de prevenção\n\nGostaria de agendar uma consulta?',
    'Excelente! Temos horários disponíveis:\n• Segunda-feira: 09:00, 14:30\n• Terça-feira: 10:00, 16:00\n• Quarta-feira: 11:00, 15:30\n\nQual horário prefere?'
  ]

  useEffect(() => {
    const timer = setInterval(() => {
      setCountdown(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 }
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 }
        } else if (prev.hours > 0) {
          return { hours: prev.hours - 1, minutes: 59, seconds: 59 }
        }
        return prev
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  useEffect(() => {
    const messageTimer = setInterval(() => {
      setCurrentMessage(prev => (prev + 1) % messages.length)
    }, 4000)

    return () => clearInterval(messageTimer)
  }, [])

  const scrollToDemo = () => {
    document.getElementById('demo')?.scrollIntoView({ behavior: 'smooth' })
  }

  return (
    <section className="hero-gradient text-white pt-24 pb-20 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-white/20 rounded-full"></div>
        <div className="absolute top-3/4 right-1/4 w-2 h-2 bg-white/20 rounded-full"></div>
        <div className="absolute top-1/2 left-1/3 w-1 h-1 bg-white/20 rounded-full"></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-white/20 rounded-full"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8">
            {/* Urgency Banner */}
            <div className="glass-effect rounded-full px-6 py-3 inline-flex items-center gap-3 pulse-animation">
              <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse"></div>
              <span className="font-semibold">ÚLTIMAS 15 VAGAS - Oferta Expira em:</span>
              <div className="flex gap-1 font-mono font-bold text-yellow-300">
                <span>{countdown.hours.toString().padStart(2, '0')}</span>:
                <span>{countdown.minutes.toString().padStart(2, '0')}</span>:
                <span>{countdown.seconds.toString().padStart(2, '0')}</span>
              </div>
            </div>

            {/* Rating */}
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <div className="flex">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>
                <span>4.9/5 (89 avaliações)</span>
              </div>
              <div className="flex items-center gap-2 text-sm opacity-90">
                <Eye className="w-4 h-4" />
                <span>18 pessoas vendo agora</span>
              </div>
            </div>

            {/* Main Headline */}
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-6xl font-extrabold leading-tight">
                Pare de Perder <span className="text-yellow-300 drop-shadow-lg">R$ 12.000/mês</span>
                <br />com Atendimento Manual
              </h1>
              
              <p className="text-xl opacity-95">
                <strong>+300 pequenas empresas</strong> já economizam <strong>12h semanais</strong> 
                e aumentaram <strong>40% na conversão</strong> com nossos chatbots inteligentes
              </p>
            </div>

            {/* Stats */}
            <div className="flex gap-8">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span>89 empresas ativas hoje</span>
              </div>
              <div className="flex items-center gap-2">
                <Users className="w-5 h-5 text-green-400" />
                <span>1.247 clientes atendidos esta semana</span>
              </div>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <Button 
                onClick={scrollToDemo}
                size="lg"
                className="bg-green-500 hover:bg-green-600 text-white px-8 py-4 text-lg font-bold rounded-full shadow-lg hover:scale-105 transition-transform"
              >
                <MessageCircle className="w-6 h-6 mr-2" />
                QUERO MINHA DEMONSTRAÇÃO GRATUITA
              </Button>
              <Button 
                variant="outline"
                size="lg"
                className="border-white/30 text-white hover:bg-white/10 px-6 py-4 text-lg font-semibold rounded-full backdrop-blur-sm"
              >
                <Play className="w-5 h-5 mr-2" />
                Ver Demo (2 min)
              </Button>
            </div>

            {/* Trust Badges */}
            <div className="grid grid-cols-2 gap-4">
              <div className="flex items-center gap-2 text-sm">
                <Shield className="w-4 h-4 text-green-400" />
                <span>Conformidade LGPD</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Calendar className="w-4 h-4 text-green-400" />
                <span>7 dias grátis</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-4 h-4 text-green-400" />
                <span>Setup em 24h</span>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Headphones className="w-4 h-4 text-green-400" />
                <span>Suporte 24/7</span>
              </div>
            </div>
          </div>

          {/* Chat Demo */}
          <div className="flex justify-center">
            <div className="bg-white rounded-3xl shadow-2xl overflow-hidden max-w-md w-full floating-animation">
              {/* Chat Header */}
              <div className="brand-gradient text-white p-4">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <Store className="w-6 h-6" />
                  </div>
                  <div>
                    <h4 className="font-semibold">Sua Empresa</h4>
                    <span className="text-sm opacity-80">Online agora</span>
                  </div>
                </div>
              </div>

              {/* Chat Messages */}
              <div className="p-5 bg-gray-50 min-h-[300px] space-y-4">
                <div className="flex gap-3">
                  <div className="w-8 h-8 brand-gradient rounded-full flex items-center justify-center flex-shrink-0">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div className="bg-white p-3 rounded-2xl shadow-sm max-w-[80%]">
                    <p className="text-gray-800 text-sm">
                      Olá! Sou o assistente virtual da sua empresa. Como posso ajudar você hoje?
                    </p>
                  </div>
                </div>

                <div className="flex justify-end">
                  <div className="brand-gradient text-white p-3 rounded-2xl max-w-[80%]">
                    <p className="text-sm">
                      Gostaria de saber mais sobre seus serviços
                    </p>
                  </div>
                </div>

                <div className="flex gap-3">
                  <div className="w-8 h-8 brand-gradient rounded-full flex items-center justify-center flex-shrink-0">
                    <Bot className="w-4 h-4 text-white" />
                  </div>
                  <div className="bg-white p-3 rounded-2xl shadow-sm max-w-[80%] typing-animation">
                    <p className="text-gray-800 text-sm whitespace-pre-line">
                      {messages[currentMessage]}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero

